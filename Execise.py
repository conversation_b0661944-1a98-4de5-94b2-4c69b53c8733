"""age = int(input("Enter number: "))
pair = age / 2

if pair != int(pair):
    print("Odd")
else:
    print("Pair")
"""



tries = 4
correct_password = "secret123"

def check_password():
    global tries

    if tries == 0:
        print("You are out of tries! Access denied.")
        return False

    print(f"You have {tries} tries left")
    password = input("Enter password: ")

    if password == correct_password:
        print("Access granted! Welcome!")
        return True
    else:
        tries -= 1
        if tries > 0:
            print(f"Wrong password! You have {tries} tries left")
        else:
            print("Wrong password! You are out of tries! Access denied.")
        return False

while tries > 0:
    if check_password():
        break  # Exit loop if password is correct
