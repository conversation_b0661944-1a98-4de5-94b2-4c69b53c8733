import os
import shutil
import random
import win32api
import win32con

def reset_attributes(path):
    """
    Resets all attributes of a given file or folder to a normal state.
    """
    try:
        if not os.path.exists(path):
            return
        
        current_attributes = win32api.GetFileAttributes(path)
        
        # Only change attributes if they are not already normal
        if current_attributes != win32con.FILE_ATTRIBUTE_NORMAL:
            win32api.SetFileAttributes(path, win32con.FILE_ATTRIBUTE_NORMAL)
            print(f"Reset attributes for: {path}")

    except Exception as e:
        print(f"Could not reset attributes for {path}: {e}")

def secure_delete_file(file_path):
    """Overwrites a file with random data before deleting it."""
    try:
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            print(f"Error: The provided path '{file_path}' is not a valid file.")
            return

        # Step 1: Remove attributes before deletion
        reset_attributes(file_path)

        # Step 2: Overwrite and delete
        file_size = os.path.getsize(file_path)
        with open(file_path, "wb") as f:
            random_bytes = os.urandom(file_size)
            f.write(random_bytes)
        
        os.remove(file_path)
        print(f"Successfully and securely deleted the file: {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

def secure_delete_folder(folder_path):
    """Recursively deletes all contents of a folder before deleting the folder itself."""
    try:
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            print(f"Error: The provided path '{folder_path}' is not a valid folder.")
            return

        # Step 1: Remove attributes from the folder itself before starting to walk
        reset_attributes(folder_path)

        # Step 2: Recursively delete contents
        for root, dirs, files in os.walk(folder_path, topdown=False):
            for name in files:
                file_path = os.path.join(root, name)
                secure_delete_file(file_path)
            for name in dirs:
                dir_path = os.path.join(root, name)
                reset_attributes(dir_path)  # Ensure subfolders are writable
                os.rmdir(dir_path)

        os.rmdir(folder_path)
        print(f"Successfully and securely deleted the folder: {folder_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

def main():
    """Main function to handle user interaction."""
    path = input("Enter the path of the file or folder you want to permanently delete: ")
    path = path.strip().strip('"') # Clean up potential quotes from drag-and-drop

    if not os.path.exists(path):
        print(f"Error: The path '{path}' does not exist.")
        return

    confirmation = input(f"Are you sure you want to permanently delete '{path}'? Type 'yes' to confirm: ").lower()

    if confirmation == 'yes':
        if os.path.isfile(path):
            reset_attributes(path)
            secure_delete_file(path)
        elif os.path.isdir(path):
            reset_attributes(path)
            secure_delete_folder(path)
        else:
            print("The provided path is neither a file nor a folder.")
    else:
        print("Deletion cancelled.")

if __name__ == "__main__":
    main()