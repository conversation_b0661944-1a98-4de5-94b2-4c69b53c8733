import os
import shutil
import random

def secure_delete_file(file_path):
    """Overwrites a file with random data before deleting it."""
    try:
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            print(f"Error: The provided path '{file_path}' is not a valid file.")
            return

        file_size = os.path.getsize(file_path)

        with open(file_path, "wb") as f:
            random_bytes = os.urandom(file_size)
            f.write(random_bytes)

        os.remove(file_path)
        print(f"Successfully and securely deleted the file: {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

def secure_delete_folder(folder_path):
    """Recursively deletes all contents of a folder before deleting the folder itself."""
    try:
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            print(f"Error: The provided path '{folder_path}' is not a valid folder.")
            return

        for root, dirs, files in os.walk(folder_path, topdown=False):
            for name in files:
                file_path = os.path.join(root, name)
                secure_delete_file(file_path)
            for name in dirs:
                dir_path = os.path.join(root, name)
                os.rmdir(dir_path)

        os.rmdir(folder_path)
        print(f"Successfully and securely deleted the folder: {folder_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

def main():
    """Main function to handle user interaction."""
    path = input("Enter the path of the file or folder you want to permanently delete: ")
    path = path.strip()

    if not os.path.exists(path):
        print(f"Error: The path '{path}' does not exist.")
        return

    confirmation = input(f"Are you sure you want to permanently delete '{path}'? Type 'yes' to confirm: ").lower()

    if confirmation == 'yes':
        if os.path.isfile(path):
            secure_delete_file(path)
        elif os.path.isdir(path):
            secure_delete_folder(path)
        else:
            print("The provided path is neither a file nor a folder.")
    else:
        print("Deletion cancelled.")

if __name__ == "__main__":
    main()